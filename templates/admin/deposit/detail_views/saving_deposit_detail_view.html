{% extends "admin/base_site.html" %}
{% load i18n static admin_urls unfold humanize %}

{% block branding %}
    {% include "unfold/helpers/site_branding.html" %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <!-- اضافه کردن فایل CSS آیکون‌های Material Symbols -->
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet" />
    <style>
      :root {
        font-family: 'Vazirmatn', system-ui, sans-serif;
      }
      
      /* Custom styles for tab system */
      .tab-content {
        display: none;
      }
      
      .tab-content.active {
        display: block;
      }
      
      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 4px;
      }
      
      .dot-success {
        background-color: var(--color-success-500);
      }
      
      .dot-warning {
        background-color: var(--color-warning-500);
      }
      
      /* اطمینان از نمایش صحیح آیکون‌ها */
      .material-symbols-outlined {
        font-variation-settings: 'FILL' 1, 'wght' 400, 'GRAD' 0, 'opsz' 24;
        vertical-align: middle;
      }
      
      /* Custom style for owner row in table */
      .owner-row {
        background-color: rgba(34, 197, 94, 0.1) !important; /* Light green background */
        border: 1px solid rgba(34, 197, 94, 0.3) !important; /* Green border */
      }
      
      /* Make sure the owner row stands out in both light and dark modes */
      .dark .owner-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.4) !important;
      }
      
      /* Add a subtle left border to make it even more distinct */
      .owner-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important;
      }
      
      /* Styles for due date rows */
      .completed-row {
        background-color: rgba(34, 197, 94, 0.1) !important; /* Light green background */
      }
      
      .pending-row {
        background-color: rgba(245, 158, 11, 0.1) !important; /* Light amber background */
      }
      
      /* Styles for loan status rows */
      .overdue-row {
        background-color: rgba(239, 68, 68, 0.1) !important; /* Light red background */
      }
      
      /* Make sure the rows stand out in dark mode */
      .dark .completed-row {
        background-color: rgba(34, 197, 94, 0.2) !important;
      }
      
      .dark .pending-row {
        background-color: rgba(245, 158, 11, 0.2) !important;
      }
      
      .dark .overdue-row {
        background-color: rgba(239, 68, 68, 0.2) !important;
      }
      
      /* Add a subtle left border to make it even more distinct */
      .completed-row td:first-child {
        border-left: 3px solid rgb(34, 197, 94) !important; /* Green border */
      }
      
      .pending-row td:first-child {
        border-left: 3px solid rgb(245, 158, 11) !important; /* Amber border */
      }
      
      .overdue-row td:first-child {
        border-left: 3px solid rgb(239, 68, 68) !important; /* Red border */
      }
      
      /* Custom button styles */
      .btn-red {
        background-color: #C25151 !important;
      }
      
      .btn-red:hover {
        background-color: #A94545 !important;
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality
        const tabButtons = document.querySelectorAll('[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
          button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
              btn.classList.remove('border-primary-500', 'font-semibold', 'text-primary-500');
              btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Hide all tab contents
            tabContents.forEach(content => {
              content.classList.remove('active');
            });
            
            // Add active class to clicked button
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-primary-500', 'font-semibold', 'text-primary-500');
            
            // Show corresponding tab content
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
          });
        });
      });
    </script>
{% endblock %}

{% block breadcrumbs %}{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_savingdeposit_changelist' as link %}
                {% trans 'Saving Deposit' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:deposit_savingdeposit_detail' object_id=deposit_id as link %}
                {% trans 'Saving Deposit Detail' as name %} 
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}
            </ul>
        </div>
    </div>
{% endif %}{% endblock %}

{% block content %}
    {% component "unfold/components/container.html" %}
        <!-- Main Deposit Information Card -->

        {% component "unfold/components/card.html" with class="mb-6" %}
        <!-- Header with Edit button on the right -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center">
                <img src="{% static 'images/deposit-saving.svg' %}" alt="Saving Deposit" class="w-16 h-18 mr-3">
                {% component "unfold/components/title.html" with variant="primary" class="text-2xl mr-4" %}
                    {{ deposit_title }}
                {% endcomponent %}
            </div>
            <div class="flex space-x-2">
                {% component "unfold/components/button.html" with href=tickets_url variant="secondary" icon="chat" class="mr-2"%}
                    {% trans "Tickets" %}
                {% endcomponent %}
                {% component "unfold/components/button.html" with href=change_form_url variant="primary" icon="pencil" class=""%}
                    {% trans "View/Edit Details" %}
                {% endcomponent %}
            </div>
        </div>
        
            <div class="p-6">                
                <!-- Grid layout using Tailwind instead of unfold/components/grid.html -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                    <!-- Deposit Balance -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">account_balance_wallet</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Deposit Balance:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                           &nbsp; {{ deposit_balance|intcomma }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Amount per Share -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">attach_money</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Amount per Share:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                            &nbsp; {{ amount_per_share|intcomma }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Payment Cycle -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">calendar_month</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Payment Cycle:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                            {{ payment_cycle }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Validity Duration -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">schedule</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Validity Duration:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                            {{ validity_duration }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}

                    <!-- Due Date -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">event_upcoming</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Due Date:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}{{ due_date }} {% endcomponent %}
                    {% endcomponent %}
                    
                    <!-- Region Name -->
                    {% component "unfold/components/flex.html" with class="justify-between items-center space-x-4" %}
                        {% component "unfold/components/flex.html" with class="items-center" %}
                            <span class="material-symbols-outlined mr-2 text-primary-500">location_on</span>
                            {% component "unfold/components/text.html" with class="whitespace-nowrap" %}{% trans "Region Name:" %}{% endcomponent %}
                        {% endcomponent %}
                        {% component "unfold/components/text.html" with class="font-semibold whitespace-nowrap" %}
                            {{ regin_name }} &nbsp;
                        {% endcomponent %}
                    {% endcomponent %}
                    
                </div>
            </div>
        {% endcomponent %}
        
        <!-- Action Buttons -->
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {% component "unfold/components/button.html" with href=payment_url variant="primary" class="justify-center" %}
                <span class="material-symbols-outlined">payments</span>
                {% trans "Payments" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=transaction_url variant="primary" class="justify-center bg-red-500 hover:bg-red-600" %}
                <span class="material-symbols-outlined">credit_card</span>
                {% trans "Transactions" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=request_join_url variant="default" class="justify-center" %}
                <span class="material-symbols-outlined">hail</span>
                {% trans "Membership Requests" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=request_loan_url variant="default" class="justify-center " %}
                <span class="material-symbols-outlined">receipt_long</span>
                {% trans "Loans" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=tickets_url variant="default" class="justify-center" %}
                <span class="material-symbols-outlined">confirmation_number</span>
                {% trans "Tickets" %}
            {% endcomponent %}
            
            {% component "unfold/components/button.html" with href=medias_url variant="default" class="justify-center" %}
                <span class="material-symbols-outlined">photo_library</span>
                {% trans "Media" %}
            {% endcomponent %}

        </div>
        
        <!-- Tab System -->
        {% component "unfold/components/card.html" %}
            <!-- Custom tab navigation -->
            <div class="border-b border-gray-200">
                <div class="flex space-x-4">
                    <button class="py-3 px-4 border-b-2 border-primary-500 font-semibold text-primary-500" data-tab="members-tab">{% trans "Members" %}</button>
                    <button class="py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="duedate-tab">{% trans "DueDates" %}</button>
                    <button class="py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="loans-tab">{% trans "Loans" %}</button>
                </div>
            </div>
            
            <!-- Tab contents -->
            <div class="p-4 mt-6">
                <!-- Members Tab -->
                <div id="members-tab" class="tab-content active">
                    {% if deposit_members %}
                        {% component "unfold/components/table.html" with table=members_table_data card_included=1 striped=4 %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="bg-white border border-base-200 flex grow items-center justify-center py-2 rounded shadow-sm dark:bg-base-900 lg:border-0 lg:rounded-none lg:shadow-none">
                                {% trans "No data" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Due Dates Tab -->
                <div id="duedate-tab" class="tab-content">
                    {% if due_dates_table_data.rows %}
                        {% component "unfold/components/table.html" with table=due_dates_table_data card_included=1 striped=1 %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="bg-white border border-base-200 flex grow items-center justify-center py-2 rounded shadow-sm dark:bg-base-900 lg:border-0 lg:rounded-none lg:shadow-none">
                                {% trans "No due dates available" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Loans Tab -->
                <div id="loans-tab" class="tab-content">
                    {% if loan_members_table_data.rows %}
                        {% component "unfold/components/table.html" with table=loan_members_table_data card_included=1 striped=1 %}{% endcomponent %}
                    {% else %}
                        <div class="text-center py-8">
                            <img src="{% static 'images/image_null_deposit.svg' %}" alt="No data" class="mx-auto h-32">
                            <p class="bg-white border border-base-200 flex grow items-center justify-center py-2 rounded shadow-sm dark:bg-base-900 lg:border-0 lg:rounded-none lg:shadow-none">
                                {% trans "No loans available" %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endcomponent %}
    {% endcomponent %}
{% endblock %}