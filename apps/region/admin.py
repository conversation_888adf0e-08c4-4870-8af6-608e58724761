from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from django import forms

from unfold.admin import ModelAdmin, TabularInline
from unfold.decorators import display, action
from unfold.widgets import UnfoldAdminSelectWidget

from apps.region.models import Region, UserRegion, InvitationLink
from utils.admin import project_admin_site, SuperAdminOnlyAdmin, RegionFilteredAdmin

class UserRegionInline(TabularInline):
    """Inline for displaying UserRegions in Region admin - all fields read-only"""
    model = UserRegion
    extra = 0  # No extra empty forms
    # can_delete = False
    tab = True
    fields = ('user', 'invited_by',  'is_active', 'created_at',)
    readonly_fields = ('user', 'invited_by',  'is_active', 'created_at',)
    
    def has_add_permission(self, request, obj=None):
        # Disable adding new InvitationLink entries
        return False
    
    def has_change_permission(self, request, obj=None):
        # Disable editing InvitationLink entries
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Disable deleting InvitationLink entries
        return False


class InvitationLinkInline(TabularInline):
    """Inline for displaying InvitationLinks in UserRegion admin"""
    model = InvitationLink
    extra = 0
    tab = True
    can_delete = False  # Disable deletion
    fields = ('invitation_code', 'is_used', 'used_by', 'created_at', 'used_at')
    readonly_fields = ('used_by', 'is_used','invitation_code', 'created_at', 'used_at')
    
    def has_add_permission(self, request, obj=None):
        # Disable adding new InvitationLink entries
        return False
    
    def has_change_permission(self, request, obj=None):
        # Disable editing InvitationLink entries
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Disable deleting InvitationLink entries
        return False


@admin.register(Region)
class RegionAdmin(SuperAdminOnlyAdmin):
    list_display = ('name', 'owner','members_count', 'created_at',)
    search_fields = ('name',)
    list_filter = ('created_at', 'updated_at')
    inlines = [InvitationLinkInline, UserRegionInline]
    autocomplete_fields = ('owner',)
    history = False

    fieldsets = (
        (None, {
            "fields": ("name", 'owner', "description"),
        }),

    )
        
    @display(description=_("Members Count"))
    def members_count(self, obj):
        # Create a link to the User admin filtered by this region
        url = reverse('admin:account_user_changelist') + f'?region_memberships__region={obj.id}'
        count = obj.members.filter(is_active=True).count()
        return format_html(
            '<a href="{}"><i class="material-icons" style="font-size: 16px;">{} </i></a>',
            url, count
        )

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        form = formset.form        
        if hasattr(form.base_fields.get('owner'), 'widget'):
            form.base_fields['owner'].widget.can_delete_related = False

        return formset

    actions_detail = [
        "generate_invitation_code",
    ]
    @action(description=_("Generate Invitation Code"))
    def generate_invitation_code(self, request, object_id):
        """Generate invitation code for current user in this region"""
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        
        # Get the region object
        obj = self.get_object(request, object_id)
        if obj is None:
            self.message_user(request, _("Region not found"), level='ERROR')
            return HttpResponseRedirect(reverse('admin:region_region_changelist'))
            
        # Check if UserRegion exists for the current user and selected region
        user_region = UserRegion.objects.filter(
            user=request.user,
            region=obj
        ).first()
        
        # If UserRegion doesn't exist, create a new one
        if not user_region:
            user_region = UserRegion.objects.create(
                user=request.user,
                region=obj,
                is_active=True,
                is_current=False
            )
        
        # Create new invitation link
        invitation_link = InvitationLink.objects.create(
            user_region=user_region,
            region=obj
        )
        
        # Generate invitation code
        invitation_link.invitation_code = invitation_link.generate_invitation_code()
        invitation_link.save()
        
        self.message_user(
            request,
            _("Invitation code generated successfully: {}").format(invitation_link.full_invitation_url)
        )
        
        # Redirect back to the region change page
        return HttpResponseRedirect(reverse('admin:region_region_change', args=[object_id]))
        

class UserRegionForm(forms.ModelForm):
    invitation_code_input = forms.CharField(
        required=False,
        help_text=_("Enter invitation code to validate and set region/invited_by automatically"),
        label=_("Invitation Code"),
        widget=forms.TextInput(attrs={'placeholder': 'Enter invitation code...'})
    )
    
    class Meta:
        model = UserRegion
        fields = '__all__'
    
    def clean_invitation_code_input(self):
        invitation_code = self.cleaned_data.get('invitation_code_input')
        
        if not invitation_code:
            return invitation_code
        
        # Validate using the model method
        is_valid, region, invited_by, error_message = UserRegion.validate_invitation_code(invitation_code)
        
        if not is_valid:
            raise forms.ValidationError(error_message)
        
        # Store validated data for use in save
        self.validated_region = region
        self.validated_invited_by = invited_by
        
        return invitation_code


@admin.register(UserRegion)
class UserRegionAdmin(RegionFilteredAdmin):
    form = UserRegionForm
    list_display = ('user', 'region', 'invited_by', 'is_active', 'created_at')
    search_fields = ('user__fullname', 'region__name')
    list_filter = ('is_active', 'is_current', 'region')
    readonly_fields = ('is_current', )
    autocomplete_fields = ('user', 'region', 'invited_by')
    inlines = [InvitationLinkInline]
    
    fieldsets = (
        (None, {
            "fields": ("user",),
        }),
        (_("Region Information"), {
            "fields": ("region", "invited_by", "is_active"),
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Override save_model to handle invitation code validation"""
        invitation_code = form.cleaned_data.get('invitation_code_input')
        
        if invitation_code and not change:  # Only for new objects
            # Use validated data from form
            if hasattr(form, 'validated_region') and hasattr(form, 'validated_invited_by'):
                obj.region = form.validated_region
                obj.invited_by = form.validated_invited_by
                
                # Check if user is already a member of this region
                existing_membership = UserRegion.objects.filter(
                    user=obj.user, 
                    region=obj.region
                ).first()
                
                if existing_membership:
                    self.message_user(
                        request,
                        _("User is already a member of this region."),
                        level='ERROR'
                    )
                    return
                
                # Mark invitation link as used if it exists
                try:
                    from apps.region.models import InvitationLink
                    invitation_link = InvitationLink.objects.get(invitation_code=invitation_code.split('/')[-1])
                    if not invitation_link.is_used:
                        invitation_link.mark_as_used(obj.user)
                        self.message_user(
                            request,
                            _("User successfully added to region using invitation code.")
                        )
                except InvitationLink.DoesNotExist:
                    pass  # It's okay, might be a UserRegion or Region invitation code
        
        super().save_model(request, obj, form, change)


class InvitationLinkAdmin(ModelAdmin):
    list_display = ('region', 'is_used', 'created_at', 'invitation_url_display')
    search_fields = ('invitation_code', 'user_region__user__fullname', 'used_by__fullname', 'region__name')
    list_filter = ('is_used', 'region', 'created_at', 'used_at')
    autocomplete_fields = ('region', 'used_by')
    
    def invitation_url_display(self, obj):
        """Display the full invitation URL"""
        return obj.full_invitation_url
    invitation_url_display.short_description = _("Invitation URL")
    
    fieldsets = (
        (None, {
            "fields": ("region",),
        }),
        (_("information"), {
            "fields": ("is_used", "used_by", "used_at", "created_at",),
        }),
    )

    readonly_fields = ('created_at', 'used_at', 'used_by', 'is_used')


    def save_model(self, request, obj, form, change):
        """Override save_model to automatically set user_region and invitation_code"""
        if not change:  # Only for new objects
            # Check if UserRegion exists for the current user and selected region
            user_region = UserRegion.objects.filter(
                user=request.user,
                region=obj.region
            ).first()
            
            # If UserRegion doesn't exist, create a new one
            if not user_region:
                user_region = UserRegion.objects.create(
                    user=request.user,
                    region=obj.region,
                    is_active=True,
                    is_current=False
                )
            
            obj.user_region = user_region
            
            # Generate invitation code
            obj.invitation_code = obj.generate_invitation_code()
        
        super().save_model(request, obj, form, change)





# Register with project_admin_site
project_admin_site.register(Region, RegionAdmin)
project_admin_site.register(UserRegion, UserRegionAdmin)
project_admin_site.register(InvitationLink, InvitationLinkAdmin)
