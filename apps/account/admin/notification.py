from django.contrib import admin
from django.utils.translation import gettext as _
import json
from django.utils.html import format_html
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import Http404
from django import forms

from apps.account.models.notification import Notification
from apps.region.models.region import UserRegion
from apps.deposit.models.deposit import Deposit
from apps.account.services import NotificationService
from unfold.admin import ModelAdmin
from unfold.decorators import action
from unfold.widgets import UnfoldAdminSelectWidget
from utils.admin import project_admin_site


class TestNotificationForm(forms.Form):
    """
    فرم تست نوتیفیکیشن برای ارسال نوتیفیکیشن‌های آزمایشی
    """
    
    # انتخاب‌های سرویس نوتیفیکیشن
    NOTIFICATION_SERVICE_CHOICES = [
        ('deposit_request_approved', _('Deposit Request Approved')),
        ('deposit_request_rejected', _('Deposit Request Rejected')),
        ('loan_request_approved', _('Loan Request Approved')),
        ('loan_request_rejected', _('Loan Request Rejected')),
        ('payment_created_notification_to_owner', _('Payment Created Notification')),
        ('withdrawal_approved_notification_to_owner', _('Withdrawal Approved Notification')),
        ('lottery_announcement_to_all_members', _('Lottery Announcement')),
        ('lottery_winner_notification', _('Lottery Winner Notification')),
        ('voting_poll_created_notification', _('Voting Poll Created')),
        ('new_ticket_message_notification', _('New Ticket Message')),
        ('withdrawal_request_created_notification', _('Withdrawal Request Created')),
    ]
    
    user_region = forms.ModelChoiceField(
        queryset=UserRegion.objects.select_related('user', 'region').filter(is_active=True).order_by('region__name', 'user__fullname'),
        label=_("User Region"),
        help_text=_("Select the user region to send notification to"),
        widget=UnfoldAdminSelectWidget(),
        required=True,
        empty_label=_("Select a user region...")
    )
    
    notification_service = forms.ChoiceField(
        choices=[('', _('Select notification service...'))] + NOTIFICATION_SERVICE_CHOICES,
        label=_("Notification Service"),
        help_text=_("Select the type of notification to send"),
        widget=UnfoldAdminSelectWidget(),
        required=True
    )

    class Media:
        js = [
            "admin/js/vendor/jquery/jquery.js",
            "admin/js/jquery.init.js",
            "admin/js/core.js",
        ]


class NotificationAdmin(ModelAdmin):
    list_display = ('title', 'user', 'is_read', 'has_data', 'created_at', 'updated_at')
    list_filter = ('is_read', 'created_at', 'updated_at')
    search_fields = ('title', 'message', 'user__fullname')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'formatted_data')
    actions_list = [
        "test_notification_action",
    ]
    fieldsets = (
        (_('Notification Information'), {
            'fields': ('title', 'message', 'user', 'is_read'),
            'classes': ['tab']
        }),
        (_('Notification Data'), {
            'fields': ('data', 'formatted_data'),
            'classes': ['tab']
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ['tab']
        }),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user')
        return queryset
    
    def formatted_data(self, obj):
        """
        Format the JSON data for better readability in the admin interface
        """
        if not obj.data:
            return "-"
        
        try:
            # Pretty format the JSON data
            formatted_json = json.dumps(obj.data, indent=4, ensure_ascii=False)
            # Replace spaces with non-breaking spaces and newlines with <br> tags
            formatted_html = formatted_json.replace(' ', '&nbsp;').replace('\n', '<br>')
            return format_html('<pre style="font-family: monospace;">{}</pre>', formatted_html)
        except Exception as e:
            return f"Error formatting data: {str(e)}"
    
    formatted_data.short_description = _("Formatted Data")
            
    def has_data(self, obj):
        """
        Check if the notification has data
        """
        if obj.data:
            return format_html('<span style="color: green;">✓</span>')
        return format_html('<span style="color: red;">✗</span>')
    
    has_data.short_description = _("Has Data")
    
    def has_test_notification_permission(self, request):
        """
        Check if the user has permission to use the test notification action
        """
        return request.user.is_superuser or request.user.has_perm('account.change_notification')
    
    @action(
        description=_("Test Notification"),
        url_path="test-notification",
        permissions=["test_notification"],
    )
    def test_notification_action(self, request):
        """
        اکشن سفارشی برای تست ارسال نوتیفیکیشن
        """
        form = TestNotificationForm(request.POST or None)

        if request.method == "POST" and form.is_valid():
            user_region = form.cleaned_data["user_region"]
            notification_service = form.cleaned_data["notification_service"]
            
            try:
                # دریافت اطلاعات کاربر و منطقه
                user = user_region.user
                region = user_region.region
                
                # پیدا کردن یک صندوق در همان منطقه (اگر وجود داشته باشد)
                deposit = Deposit.objects.filter(region=region).first()
                deposit_id = deposit.id if deposit else None
                
                # ارسال نوتیفیکیشن بر اساس سرویس انتخاب شده
                success = self._send_test_notification(
                    user.id, 
                    notification_service, 
                    region.id, 
                    deposit_id
                )
                
                if success:
                    messages.success(
                        request, 
                        _("Test notification sent successfully to user: {} in region: {}").format(
                            user.fullname, region.name
                        )
                    )
                else:
                    messages.error(
                        request, 
                        _("Failed to send test notification. Please check the logs.")
                    )
                    
            except Exception as e:
                messages.error(
                    request, 
                    _("Error sending test notification: {}").format(str(e))
                )

            return redirect(reverse_lazy("admin:account_notification_changelist"))

        # آمار برای نمایش در template
        from apps.region.models.region import UserRegion
        from apps.account.models.notification import Notification
        
        active_regions_count = UserRegion.objects.filter(is_active=True).count()
        total_notifications_count = Notification.objects.count()
        available_services_count = len(TestNotificationForm.NOTIFICATION_SERVICE_CHOICES)

        return render(
            request,
            "admin/account/test_notification_action.html",
            {
                "form": form,
                "title": _("Test Notification"),
                "opts": self.model._meta,
                "active_regions_count": active_regions_count,
                "total_notifications_count": total_notifications_count,
                "available_services_count": available_services_count,
                **self.admin_site.each_context(request),
            },
        )
    
    def _send_test_notification(self, user_id, service_type, region_id, deposit_id):
        """
        ارسال نوتیفیکیشن تست بر اساس نوع سرویس انتخاب شده
        """
        try:
            if service_type == 'deposit_request_approved' and deposit_id:
                NotificationService.send_deposit_request_approved(user_id, deposit_id)
                
            elif service_type == 'deposit_request_rejected':
                NotificationService.send_deposit_request_rejected(user_id, deposit_id)
                
            elif service_type == 'loan_request_approved' and deposit_id:
                NotificationService.send_loan_request_approved(user_id, deposit_id)
                
            elif service_type == 'loan_request_rejected' and deposit_id:
                NotificationService.send_loan_request_rejected(user_id, deposit_id)
                
            elif service_type == 'payment_created_notification_to_owner' and deposit_id:
                NotificationService.send_payment_created_notification_to_owner(deposit_id)
                
            elif service_type == 'withdrawal_approved_notification_to_owner' and deposit_id:
                NotificationService.send_withdrawal_approved_notification_to_owner(deposit_id)
                
            elif service_type == 'lottery_announcement_to_all_members' and deposit_id:
                NotificationService.send_lottery_announcement_to_all_members(deposit_id)
                
            elif service_type == 'lottery_winner_notification' and deposit_id:
                NotificationService.send_lottery_winner_notification(deposit_id, user_id)
                
            elif service_type == 'voting_poll_created_notification' and deposit_id:
                NotificationService.send_voting_poll_created_notification(deposit_id)
                
            elif service_type == 'new_ticket_message_notification':
                NotificationService.send_new_ticket_message_notification([user_id], region_id)
                
            elif service_type == 'withdrawal_request_created_notification' and deposit_id:
                NotificationService.send_withdrawal_request_created_notification(user_id, deposit_id)
                
            else:
                # اگر deposit_id وجود نداشت، یک نوتیفیکیشن عمومی ارسال می‌کنیم
                NotificationService._send_and_save_notification(
                    user_id=user_id,
                    title=_("Test Notification"),
                    body=_("This is a test notification for service: {}").format(service_type),
                    data={
                        "action": "test_notification",
                        "service_type": service_type,
                        "region_id": region_id,
                        "help": "Test notification sent from admin panel"
                    }
                )
            
            return True
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in _send_test_notification: {str(e)}")
            return False


project_admin_site.register(Notification, NotificationAdmin)
