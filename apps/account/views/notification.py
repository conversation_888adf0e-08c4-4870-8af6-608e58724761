
from rest_framework import generics, status
from rest_framework.response import Response
from apps.account.doc import (
    notification_list_swagger, notification_read_all_swagger,
    send_notification_swagger, send_bulk_notification_swagger
)
from rest_framework.permissions import IsAuthenticated

from apps.account.serializers import NotificationSerializer, NotificationSendSerializer
from apps.account.models import Notification, User
from apps.account.tasks import send_notification, send_bulk_notification



class NotificationListView(generics.ListAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated,] 

    @notification_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        user = self.request.user
        queryset = Notification.objects.filter(user=user)

        return queryset.order_by('-created_at')


class NotificationReadAllView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated,]
    queryset = Notification.objects.all()

    @notification_read_all_swagger
    def get(self, request, *args, **kwargs):
        user = request.user

        # Get base queryset for user's notifications
        notifications = Notification.objects.filter(user=user)

        # Update the filtered notifications
        notifications.update(is_read=True)

        return Response({'status': "all notifications marked as read"}, status=status.HTTP_200_OK)
    
    
    
    
class SendNotificationView(generics.GenericAPIView):

    @send_notification_swagger
    def post(self, request, *args, **kwargs):

        user_id = request.data.get('user_id', 1)
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)
        
        notification_title = request.data.get('title', 'test qatreh')
        notification_body = request.data.get('body', 'test qatreh body')
        data_payload = request.data.get('data', {'slam':'qatreh'})

        fcm_token = user.fcm  # Ensure that 'fcm' is a field in your User model

        if not fcm_token:
            return Response({
                'error': 'FCM token not available for this user.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            res = send_notification([fcm_token], notification_title, notification_body, data_payload)
            print(f'--->>>>>>>>>>res: {res}')
            return Response({
                'message': 'Notification sent successfully.'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SendBulkNotificationView(generics.GenericAPIView):


    @send_bulk_notification_swagger
    def post(self, request, *args, **kwargs):
        
        notification_title = request.data.get('title', 'test qatreh')
        notification_body = request.data.get('body', 'test qatreh body')
        data_payload = request.data.get('data', {'slam':'qatreh'})

        # fcm_tokens = User.objects.values_list('fcm', flat=True).distinct()
        fcm_tokens = list(User.objects.values_list('fcm', flat=True).distinct())

        if not fcm_tokens:
            return Response({
                'error': 'FCM token not available for this user.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
       
            res = send_bulk_notification.delay(fcm_tokens, notification_title, notification_body, data_payload)
            print(f'--->>>>>>>>>>res: {res}')
            return Response({
                'message': 'Notification sent successfully.'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            print(f'--->>>>>>>>>>e: {e}')
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



