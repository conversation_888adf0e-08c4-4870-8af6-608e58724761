from rest_framework import serializers
from rest_framework.authtoken.models import Token
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from apps.account.models import User
from utils import FileFieldSerializer, absolute_url
from utils.validators import validate_type_code
from apps.ticket.models import TicketMessage, Ticket



class UserProfileSerializer(serializers.ModelSerializer):
    avatar = FileFieldSerializer(required=False)
    password = serializers.CharField(write_only=True, required=False, validators=[validate_password])
    fullname = serializers.CharField(required=False)
    gender = serializers.ChoiceField(
        choices=User.GenderChoices.choices, 
        required=False,  
        help_text="Select the user's gender."  
    )
    is_active_in_region = serializers.SerializerMethodField()
    invitation_code = serializers.SerializerMethodField()
    region_name = serializers.SerializerMethodField()
    is_ticket = serializers.SerializerMethodField()
    unread_ticket_count = serializers.SerializerMethodField()
    unread_notification_count = serializers.SerializerMethodField()
    name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    family = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    can_create_invitation_link = serializers.SerializerMethodField()
    invited_by_fullname = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'fullname', 'name', 'family', 'avatar', 'email', 'phone_number', 'password', 'city', 'birthdate',
            'gender', 'shba_number', 'is_active_in_region', 'invitation_code', 'region_name', 'is_ticket', 'unread_ticket_count',
            'unread_notification_count', 'can_create_invitation_link', 'invited_by_fullname'
        ]
        read_only_fields = ['phone_number',]  


    def to_representation(self, instance):
        """
        Override to_representation to handle name and family fields intelligently
        """
        data = super().to_representation(instance)

        # Handle name field
        if not instance.first_name and instance.fullname:
            fullname_parts = instance.fullname.strip().split()
            if fullname_parts:
                data['name'] = fullname_parts[0]
        else:
            data['name'] = instance.first_name

        # Handle family field
        if not instance.last_name and instance.fullname:
            fullname_parts = instance.fullname.strip().split()
            if len(fullname_parts) > 1:
                data['family'] = ' '.join(fullname_parts[1:])
            else:
                data['family'] = None
        else:
            data['family'] = instance.last_name

        return data

    def get_unread_ticket_count(self, obj):
        """
        Returns the count of unread ticket messages that were sent by someone else.
        """
        # Check if obj is a User instance before proceeding
        if not isinstance(obj, User):
            return 0
        
        # استفاده از متد کلاس Ticket برای محاسبه تعداد پیام‌های خوانده نشده
        from apps.ticket.models import Ticket
        return Ticket.count_unread_messages(obj)

    def get_unread_notification_count(self, obj):
        """
        Returns the count of unread notifications for this user.
        """
        # Check if obj is a User instance before proceeding
        if not isinstance(obj, User):
            return 0
        
        # استفاده از متد کلاس User برای محاسبه تعداد نوتیفیکیشن‌های خوانده نشده
        return obj.count_unread_notifications()


    def get_region_name(self, obj):
        try:
            current_membership = obj.current_region_membership
            return current_membership.region.name if current_membership else None
        except Exception as exp:
            return None
        
    def get_invitation_code(self, obj):
        try:
            from apps.region.models import InvitationLink
            current_membership = obj.current_region_membership
            if current_membership:
                # Get the latest invitation link for this user's current region
                invitation_link = InvitationLink.objects.filter(
                    user_region=current_membership
                ).order_by('-created_at').first()
                if invitation_link:
                    from django.conf import settings
                    return f"{settings.INVITATION_LINK_DOMAIN}{invitation_link.invitation_code}"
            return None
        except Exception:
            return None
        
    def get_is_active_in_region(self, obj):
        # Check if the user has a current region membership and if it's active
        try:
            current_membership = obj.current_region_membership
            return current_membership.is_active if current_membership else False
        except:
            return False

    def validate_email(self, value):
        # Get the current instance if we're updating
        instance = getattr(self, 'instance', None)
        
        # Check if email exists and belongs to a different user
        if User.objects.filter(email=value).exclude(id=instance.id if instance else None).exists():
            raise serializers.ValidationError("This email is already registered.")
        return value
        
    def get_is_ticket(self, obj):
        """
        Returns True if the user has any unread ticket messages that were sent by someone else.
        Returns False otherwise.
        """
        # Check if obj is a User instance before proceeding
        if not isinstance(obj, User):
            return False

        # استفاده از متد get_unread_ticket_count که قبلاً تعریف کردیم
        # اگر تعداد پیام‌های خوانده نشده بیشتر از صفر باشد، True برمی‌گرداند
        unread_count = self.get_unread_ticket_count(obj)
        return unread_count > 0

    def get_can_create_invitation_link(self, obj):
        """
        Returns True if the user can create invitation links.
        User can create invitation links if:
        1. User is owner of any active deposit, OR
        2. User has ADMIN or OWNER role in any active deposit membership, OR
        3. User is owner of any region
        """
        # Check if obj is a User instance before proceeding
        if not isinstance(obj, User):
            return False

        from apps.deposit.models import Deposit, DepositMembership
        from apps.region.models import Region

        # Check if user is owner of any active deposit
        if Deposit.objects.filter(owner=obj, is_active=True).exists():
            return True

        # Check if user has ADMIN or OWNER role in any active deposit membership
        if DepositMembership.objects.filter(
            user=obj,
            is_active=True,
            deposit__is_active=True,
            role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER]
        ).exists():
            return True

        # Check if user is owner of any region
        if Region.objects.filter(owner=obj).exists():
            return True

        return False

    def get_invited_by_fullname(self, obj):
        """
        Returns the fullname of the user who invited this user to the region.
        Returns None if the user was not invited by anyone or has no region membership.
        """
        # Check if obj is a User instance before proceeding
        if not isinstance(obj, User):
            return None

        try:
            # Get the user's first region membership (oldest one)
            first_membership = obj.region_memberships.order_by('created_at').first()
            if first_membership and first_membership.invited_by:
                return first_membership.invited_by.fullname
        except Exception:
            pass

        return None

    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        if password:
            instance.set_password(password)

        # Handle name and family fields to auto-generate fullname
        # Since name and family are mapped to first_name and last_name via source,
        # they will be in validated_data as first_name and last_name
        first_name = validated_data.get('first_name')
        last_name = validated_data.get('last_name')

        # If first_name or last_name is being updated, regenerate fullname
        if first_name is not None or last_name is not None:
            # Use existing values if new ones are not provided
            name = first_name if first_name is not None else (instance.first_name or '')
            family = last_name if last_name is not None else (instance.last_name or '')

            # Generate fullname
            if name or family:
                fullname_parts = []
                if name.strip():
                    fullname_parts.append(name.strip())
                if family.strip():
                    fullname_parts.append(family.strip())
                validated_data['fullname'] = ' '.join(fullname_parts)
            else:
                # If both are empty, use phone number
                validated_data['fullname'] = str(instance.phone_number)

        # Update other fields
        for attr, value in validated_data.items():
            if value is not None:
                setattr(instance, attr, value)

        instance.save()
        return instance


class UserRegisterSerializer(serializers.ModelSerializer):
    name = serializers.CharField(
        max_length=150,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="User's first name - will be stored in first_name field and used to generate fullname"
    )
    family = serializers.CharField(
        max_length=150,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="User's last name - will be stored in last_name field and used to generate fullname"
    )
    fcm = serializers.CharField(
        required=False,
        help_text="Firebase Cloud Messaging token for push notifications"
    )
    device_id = serializers.CharField(
        required=False,
        help_text="Device identifier for the user's device"
    )

    class Meta:
        model = User
        fields = ['id', 'phone_number', 'fullname', 'name', 'family', 'password', 'fcm', 'device_id']
        extra_kwargs = {
            'fullname': {'required': False, 'read_only': True, 'help_text': 'Full name of the user - auto-generated from name and family'},
            'phone_number': {
                'required': True,
                'help_text': 'User phone number in international format (e.g., +989123456789)',
                'error_messages': {
                    'unique': 'This phone number is already registered.',
                }
            },
            'password': {'required': True, 'help_text': 'User password (minimum 8 characters)'},
        }

    def validate(self, data):
        password = data.get('password')

        # Validate password length
        if password and len(password) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")

        # Generate fullname from name and family
        name = data.get('name', '').strip()
        family = data.get('family', '').strip()

        if name or family:
            # Combine name and family to create fullname
            fullname_parts = []
            if name:
                fullname_parts.append(name)
            if family:
                fullname_parts.append(family)
            data['fullname'] = ' '.join(fullname_parts)
        else:
            # If neither name nor family provided, use phone number as fallname
            data['fullname'] = data.get('phone_number', '')

        # Store name and family in appropriate fields
        data['first_name'] = name
        data['last_name'] = family

        # Remove fields that are not part of the User model
        data.pop('fcm', None)
        data.pop('device_id', None)
        data.pop('name', None)  # Remove since we stored it in first_name
        data.pop('family', None)  # Remove since we stored it in last_name

        return data

    
class UserVerifySerializer(serializers.ModelSerializer):
    code = serializers.CharField(
        max_length=5,
        validators=[validate_type_code],
        help_text="5-digit OTP verification code sent to phone number"
    )
    phone_number = serializers.CharField(
        help_text="User phone number in international format (e.g., +989123456789)"
    )
    name = serializers.CharField(
        max_length=150,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="User's first name - will be stored in first_name field and used to generate fullname"
    )
    family = serializers.CharField(
        max_length=150,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="User's last name - will be stored in last_name field and used to generate fullname"
    )
    fcm = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="Firebase Cloud Messaging token for push notifications"
    )
    timezone = serializers.CharField(
        max_length=60,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="User's timezone (e.g., Asia/Tehran)"
    )
    mobile_device_id = serializers.CharField(
        max_length=254,
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="Mobile device identifier"
    )

    class Meta:
        model = User
        fields = ["phone_number", "code", 'name', 'family', 'mobile_device_id', 'timezone', 'fcm']
        extra_kwargs = {
            'phone_number': {'required': True, 'help_text': 'User phone number used during registration'},
            'code': {'required': True, 'help_text': '5-digit OTP verification code'},
        }


class UserLoginSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    phone_number = serializers.CharField()
    token = serializers.CharField(allow_null=True, read_only=True, required=False)
    fullname = serializers.CharField(allow_null=True, read_only=True, required=False)
    avatar = serializers.CharField(allow_null=True, read_only=True, required=False)
    password = serializers.CharField(style={'input_type': 'password'}, trim_whitespace=False)
    fcm = serializers.CharField(required=False)
    timezone = serializers.CharField(
        max_length=60, 
        required=False, 
        allow_null=True, 
        allow_blank=True
    )
    mobile_device_id = serializers.CharField(
        max_length=254, 
        required=False, 
        allow_null=True, 
        allow_blank=True
    )
    
    class Meta:
        model = User
        fields = ['id', 'phone_number', 'password', 'fullname', 'avatar', 'token', 'fcm', 'mobile_device_id', 'timezone']
        
    def get_token(self, obj):
        token, created = Token.objects.get_or_create(user=obj)
        return token.key

    # def validate(self, data):
    #     data.pop('fcm', None)        
    #     data.pop('device_id', None)        
    #     return data


        
        
class UserRecoverPasswordSerializer(serializers.ModelSerializer):
    phone_number = serializers.CharField()
    
    class Meta:
        model = User
        fields = ['phone_number',]
        extra_kwargs = {
            'phone_number': {'required': True,},
        }


class UserResetPasswordSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    password_confirmation = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ['password', 'password_confirmation']
        extra_kwargs = {
            'password': {'required': True,},
            'password_confirmation': {'required': True,},
        }
                
            
    def validate(self, data):
        password = data.get('password')
        password_confirmation = data.get('password_confirmation')
        errors = {}

        if password and password_confirmation  and password != password_confirmation:
            raise serializers.ValidationError("Passwords do not match.")
        if len(password) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")
        # If there are any errors, raise ValidationError

        data.pop('password_confirmation', None)
        return data

        
class UserFCMSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['fcm']
