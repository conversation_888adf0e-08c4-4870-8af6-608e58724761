import json
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from utils.json_editor_field import JsonEditorWidget
from django.utils.safestring import mark_safe
from django import forms

from apps.deposit.models import Deposit, DepositMembership, PollDeposit, SavingDeposit, ReportingDeposit
from utils.schema import get_rules_deposit_schema


class DepositMembershipInline(admin.TabularInline):
    model = DepositMembership
    extra = 0
    readonly_fields = ('user', 'role', 'requested_unit_count', 'monthly_installment_amount', 'is_active', 'joined_date')
    fields = ('user', 'role', 'requested_unit_count', 'monthly_installment_amount', 'is_active', 'joined_date')
    can_delete = False
    show_change_link = True
    verbose_name = _("Member")
    verbose_name_plural = _("Members")

    def has_add_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')



class DepositForm(forms.ModelForm):
    class Meta:
        model = Deposit
        fields = '__all__'
        widgets = {
            'rules': JsonEditorWidget(attrs={'schema': get_rules_deposit_schema(), }),
        }



@admin.register(Deposit)
class DepositAdmin(AjaxDatatable):
    form = DepositForm
    list_display = (
        'title', 'owner', 'deposit_type', 'unit_amount', 'payment_cycle', 
        'is_active', 'created', 'updated'
    )
    list_filter = ('is_active', 'payment_cycle')
    search_fields = ('title', 'owner__fullname', 'description')
    autocomplete_fields = ('owner', 'region')

    # def has_add_permission(self, request):
        # return False  
    # def has_view_permission(self, request, obj=None):
        # return False
    def get_model_perms(self, request):
        return {}

    def get_queryset(self, request):
        # ابتدا سررسیدهای ماه گذشته را غیرفعال کن
        Deposit.disable_previous_month_due_dates_for_all()
        
        # سپس queryset پیشفرض را برگردان
        return super().get_queryset(request)



@admin.register(PollDeposit)
class PollDepositAdmin(AjaxDatatable):
    form = DepositForm
    list_display = (
        'title', 'owner', 'deposit_type', 'unit_amount', 'payment_cycle',
        'is_active', 'created', 'updated'
    )
    list_filter = ('is_active', 'payment_cycle')
    search_fields = ('title', 'owner__fullname', 'description')
    autocomplete_fields = ('owner', 'region')
    inlines = [DepositMembershipInline]
    
    # Unfold tabs configuration
    tabs = [
        {
            "title": _("General Information"),
            "fieldsets": [
                (None, {'fields': ('owner', 'title', 'description', 'is_active', 'region')}),
            ],
        },
        {
            "title": _("Financial Details"),
            "fieldsets": [
                (_('Financial Details'), {
                    'fields': (
                        'total_debt_amount', 'unit_amount', 'payment_cycle',
                        'max_unit_per_request', 'max_members_count',
                    )
                }),
            ],
        },
        {
            "title": _("Dates & Lottery"),
            "fieldsets": [
                (_('Dates and Duration'), {
                    'fields': (
                        'initial_lottery_date', 'lottery_month_count',
                    )
                }),
            ],
        },
        {
            "title": _("Rules"),
            "fieldsets": [
                (_('Rules'), {'fields': ('rules',)}),
            ],
        },
        {
            "title": _("Members"),
            "inlines": ["DepositMembershipInline"],
        },
    ]
    
    # Keep the fieldsets for non-tabbed views or fallback
    fieldsets = (
        (None, {'fields': ('owner', 'title', 'description', 'is_active', 'region')}),
        (_('Financial Details'), {
            'fields': (
                'total_debt_amount', 'unit_amount', 'payment_cycle',
                'max_unit_per_request', 'max_members_count',
            )
        }),
        (_('Dates and Duration'), {
            'fields': (
                'initial_lottery_date', 'lottery_month_count',
            )
        }),
        (_('Rules'), {'fields': ('rules',)}),
    )
    ordering = ('-created',)
    date_hierarchy = 'created'

    def get_queryset(self, request):
        return super().get_queryset(request).filter(deposit_type=Deposit.DepositType.POLL)


@admin.register(SavingDeposit)
class SavingDepositAdmin(AjaxDatatable):
    form = DepositForm

    list_display = (
        'title', 'owner', 'deposit_type', 'unit_amount', 'payment_cycle',
        'is_active', 'created', 'updated'
    )
    list_filter = ('is_active', 'payment_cycle')
    search_fields = ('title', 'owner__fullname', 'description')
    autocomplete_fields = ('owner', 'region')
    inlines = [DepositMembershipInline]

    fieldsets = (
        (None, {'fields': ('owner', 'title', 'description', 'is_active', 'region')}),
        (_('Financial Details'), {
            'fields': (
                'total_debt_amount', 'unit_amount', 'payment_cycle',
                'max_unit_per_request', 'max_members_count',
            )
        }),
        (_('Dates and Duration'), {
            'fields': (
                'start_date', 'validity_duration',
            )
        }),
        (_('Rules'), {'fields': ('rules',)}),
    )
    ordering = ('-created',)
    date_hierarchy = 'created'

    def get_queryset(self, request):
        return super().get_queryset(request).filter(deposit_type=Deposit.DepositType.SAVING)


@admin.register(ReportingDeposit)
class ReportingDepositAdmin(AjaxDatatable):
    form = DepositForm
    list_display = (
        'title', 'owner', 'deposit_type', 'unit_amount', 'payment_cycle',
        'is_active', 'created', 'updated'
    )
    list_filter = ('is_active', 'payment_cycle')
    search_fields = ('title', 'owner__fullname', 'description')
    autocomplete_fields = ('owner', 'region')
    inlines = [DepositMembershipInline]

    fieldsets = (
        (None, {'fields': ('owner', 'title', 'description', 'is_active', 'region')}),
        (_('Financial Details'), {
            'fields': (
                'total_debt_amount', 'unit_amount', 'payment_cycle',
                'max_unit_per_request', 'max_members_count',
            )
        }),
        (_('Rules'), {'fields': ('rules',)}),
    )
    ordering = ('-created',)
    date_hierarchy = 'created'

    def get_queryset(self, request):
        return super().get_queryset(request).filter(deposit_type=Deposit.DepositType.REPORTING)



@admin.register(DepositMembership)
class DepositMembershipAdmin(AjaxDatatable):
    list_display = (
        'user', 'deposit', 'role', 'requested_unit_count',
        'monthly_installment_amount', 'is_active', 'joined_date'
    )
    list_filter = ('role', 'is_active', 'deposit')
    search_fields = ('user__username', 'deposit__title')
    raw_id_fields = ('user', 'deposit')
    fieldsets = (
        (None, {
            'fields': ('user', 'deposit', 'role', 'is_active')
        }),
        (_('Financial Information'), {
            'fields': ('requested_unit_count', 'monthly_installment_amount'),
        }),
    )
    ordering = ('-joined_date',)
    
    

