from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, serializers
from rest_framework.permissions import IsAuthenticated
from django.db.models import Case, When, Value, IntegerField
from itertools import chain
from apps.deposit.doc import user_deposits_requests_swagger
from datetime import datetime

from apps.deposit.models import Deposit, DepositMembership
from apps.deposit.serializers import DepositSerializer, DepositMembershipSerializer
from apps.request.models import RequestJoinDeposit, RequestCreateDeposit
from apps.request.serializers.deposit_list import RequestJoinDepositSerializer


class UserDepositItemSerializer(DepositSerializer):
    """Serializer for deposits in the user's combined list"""
    item_type = serializers.CharField(default="deposit")
    sort_order = serializers.IntegerField(default=2)  # Middle priority
    status = serializers.CharField(default="approved", allow_null=True)
    rejection_reason = serializers.Char<PERSON>ield(default=None, allow_null=True)
    requested_unit_count = serializers.IntegerField(default=None, allow_null=True)
    monthly_installment_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=None, allow_null=True)
    created_at = serializers.DateTimeField(source='created')
    total_withdrawals = serializers.SerializerMethodField()
    due_date = serializers.SerializerMethodField()

    class Meta(DepositSerializer.Meta):
        fields = ['id', 'title', 'deposit_type', 'description', 'unit_amount', 'created_at',
                 'item_type', 'sort_order', 'status', 'rejection_reason', 'balance', 'total_income',
                 'requested_unit_count', 'monthly_installment_amount', 'total_withdrawals', 'due_date']

    def get_total_withdrawals(self, obj):
        from apps.transaction.models import Transaction
        return Transaction.get_total_withdrawals(obj)

    def get_due_date(self, obj):
        # Use the existing get_due_date method from DepositSerializer
        return super().get_due_date(obj)


class RequestJoinDepositItemSerializer(serializers.ModelSerializer):
    """Serializer for join requests in the user's combined list"""
    item_type = serializers.CharField(default="request")
    sort_order = serializers.SerializerMethodField()
    title = serializers.CharField(source="deposit.title")
    deposit_type = serializers.CharField(source="deposit.deposit_type")
    description = serializers.CharField(source="deposit.description", default="")
    unit_amount = serializers.DecimalField(source="deposit.unit_amount", max_digits=15, decimal_places=2)
    balance = serializers.SerializerMethodField()
    total_income = serializers.SerializerMethodField()
    total_withdrawals = serializers.SerializerMethodField()
    due_date = serializers.SerializerMethodField()

    class Meta:
        model = RequestJoinDeposit
        fields = ['id', 'title', 'deposit_type', 'description', 'unit_amount', 'created_at',
                 'item_type', 'sort_order', 'status', 'rejection_reason', 'balance', 'total_income',
                 'requested_unit_count', 'monthly_installment_amount', 'total_withdrawals', 'due_date']

    def get_sort_order(self, obj):
        if obj.status == RequestJoinDeposit.StatusChoices.PENDING:
            return 1  # High priority
        elif obj.status == RequestJoinDeposit.StatusChoices.REJECTED:
            return 3  # Low priority
        elif obj.status == RequestJoinDeposit.StatusChoices.CANCELLED:
            return 4  # Cancelled requests
        return 5  # Approved (should be filtered out)

    def get_balance(self, obj):
        return None

    def get_total_income(self, obj):
        return None

    def get_total_withdrawals(self, obj):
        return None

    def get_due_date(self, obj):
        return None


class RequestCreateDepositItemSerializer(serializers.ModelSerializer):
    """Serializer for create deposit requests in the user's combined list"""
    item_type = serializers.CharField(default="request")
    sort_order = serializers.SerializerMethodField()
    title = serializers.CharField(source="deposit.title")
    deposit_type = serializers.CharField(source="deposit.deposit_type")
    description = serializers.CharField(source="deposit.description", default="")
    unit_amount = serializers.DecimalField(source="deposit.unit_amount", max_digits=15, decimal_places=2)
    balance = serializers.SerializerMethodField()
    total_income = serializers.SerializerMethodField()
    total_withdrawals = serializers.SerializerMethodField()
    due_date = serializers.SerializerMethodField()
    requested_unit_count = serializers.IntegerField(default=None, allow_null=True)
    monthly_installment_amount = serializers.DecimalField(max_digits=15, decimal_places=2, default=None, allow_null=True)

    class Meta:
        model = RequestCreateDeposit
        fields = ['id', 'title', 'deposit_type', 'description', 'unit_amount', 'created_at',
                 'item_type', 'sort_order', 'status', 'rejection_reason', 'balance', 'total_income',
                 'requested_unit_count', 'monthly_installment_amount', 'total_withdrawals', 'due_date']

    def get_sort_order(self, obj):
        if obj.status == RequestCreateDeposit.StatusChoices.PENDING:
            return 1  # High priority
        elif obj.status == RequestCreateDeposit.StatusChoices.REJECTED:
            return 3  # Low priority
        return 4  # Approved (should be filtered out)

    def get_balance(self, obj):
        return None

    def get_total_income(self, obj):
        return None

    def get_total_withdrawals(self, obj):
        return None

    def get_due_date(self, obj):
        return None


class UserDepositsAndRequestsView(APIView):
    permission_classes = [IsAuthenticated]
    
    @user_deposits_requests_swagger
    def get(self, request):
        user = request.user
        
        # Get user's active deposits (through membership)
        user_deposits = Deposit.objects.filter(
            members__user=user,
            members__is_active=True,
            is_active=True
        ).distinct()
        
        # Get user's pending and rejected join requests
        join_requests = RequestJoinDeposit.objects.filter(
            user=user
        ).exclude(
            status=RequestJoinDeposit.StatusChoices.APPROVED
        )
        
        # Get user's pending and rejected create deposit requests
        create_requests = RequestCreateDeposit.objects.filter(
            user=user
        ).exclude(
            status=RequestCreateDeposit.StatusChoices.APPROVED
        )
        
        # Serialize each type of item
        deposit_items = UserDepositItemSerializer(user_deposits, many=True, context={'request': request}).data
        join_request_items = RequestJoinDepositItemSerializer(join_requests, many=True).data
        create_request_items = RequestCreateDepositItemSerializer(create_requests, many=True).data
        
        # Combine all items
        all_items = list(chain(deposit_items, join_request_items, create_request_items))
        print(f'---> {deposit_items}')
        # Sort by sort_order (1: pending requests, 2: active deposits, 3: rejected requests)
        # and then by created_at (newest first)
        def get_sort_key(item):
            # Primary sort by sort_order
            primary_key = item['sort_order']

            # Secondary sort by created_at (newest first)
            secondary_key = 0
            created_at = item.get('created_at')
            if created_at:
                try:
                    # Try to parse the datetime string
                    if isinstance(created_at, str):
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        secondary_key = -dt.timestamp()  # Negative for descending order
                except (ValueError, TypeError):
                    pass

            return (primary_key, secondary_key)

        sorted_items = sorted(all_items, key=get_sort_key)
        
        return Response(sorted_items)