import json
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from utils.json_editor_field import JsonEditorWidget
from django.utils.safestring import mark_safe
from django import forms

from apps.lottery.models import DepositLottery


# @admin.register(DepositLottery)
class DepositLotteryAdmin(AjaxDatatable):
    list_display = (
        'deposit', 
        'deposit_membership', 
        'due_date', 
        'created_at', 
        'formatted_winner_name', 
        'formatted_deposit_title'
    )
    # autocomplete_fields = ('deposit', 'deposit_membership', 'due_date')
    list_filter = ('deposit', 'due_date', 'created_at')
    search_fields = (
        'deposit__title', 
        'deposit_membership__user__username', 
        'due_date__due_date_number'
    )
    readonly_fields = ('created_at',)
    fieldsets = (
        (None, {
            'fields': ('deposit', 'deposit_membership', 'due_date')
        }),
        (_('Timestamps'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'

    @admin.display(description=_('Winner Name'))
    def formatted_winner_name(self, obj):
        return obj.deposit_membership.user.username

    @admin.display(description=_('Deposit Title'))
    def formatted_deposit_title(self, obj):
        return obj.deposit.title

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'deposit', 
            'deposit_membership__user', 
            'due_date'
        )