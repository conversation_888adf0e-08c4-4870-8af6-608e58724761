from django.utils.translation import gettext_lazy as _




def get_rules_deposit_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        "required_by_default": 1,
        'items': {
            'type': 'object',
            'title': str(_(' ')),
            'properties': {
                'subject': {'type': 'string', "format": "textarea",'title': str(_('Subject'))},
                'description': {
                    'type': "string",
                    "format": "textarea",
                    'title': str(_('Description'))
                }
            }
        }
    }
